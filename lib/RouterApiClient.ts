import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { EventEmitter } from 'events';
import * as CryptoJS from 'crypto-js';

// Define interfaces for router data
export interface RouterDevice {
  mac: string;
  ip: string;
  name: string;
  online: boolean;
  type: string;
  connectionType: 'wired' | 'wireless';
  signalStrength?: number;
}

export interface RouterStatus {
  uptime: number;
  firmwareVersion: string;
  model: string;
  wanIp: string;
  lanIp: string;
  connectedDevices: RouterDevice[];
  cpuUsage: number;
  memoryUsage: number;
  guestWifiEnabled: boolean;
}

export interface RouterSettings {
  ssid: string;
  password: string;
  guestSsid: string;
  guestPassword: string;
  guestWifiEnabled: boolean;
}

/**
 * API client for Ruijie X32-PRO router
 */
export class RouterApiClient extends EventEmitter {
  private readonly axios: AxiosInstance;
  private readonly ip: string;
  private readonly username: string;
  private readonly password: string;
  private token: string | null = null;
  private sessionId: string | null = null;
  private isConnected: boolean = false;
  private connectionCheckInterval: NodeJS.Timeout | null = null;
  private readonly connectionCheckIntervalMs: number = 30000; // 30 seconds

  constructor(ip: string, username: string, password: string) {
    super();
    this.ip = ip;
    this.username = username || 'admin';
    this.password = password;

    // Create axios instance with default configuration for Ruijie API
    this.axios = axios.create({
      baseURL: `http://${ip}`,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      withCredentials: true,
    });

    // Add response interceptor to handle authentication errors
    this.axios.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response && (error.response.status === 401 || error.response.status === 403)) {
          // Try to re-authenticate
          try {
            await this.login();
            // Retry the original request
            return this.axios.request(error.config);
          } catch (loginError) {
            return Promise.reject(loginError);
          }
        }
        return Promise.reject(error);
      }
    );
  }

  /**
   * Initialize the connection to the router
   */
  public async init(): Promise<void> {
    try {
      console.log(`[RouterApiClient] Initializing connection to router at ${this.ip}`);
      await this.login();
      console.log(`[RouterApiClient] Successfully logged in to router at ${this.ip}`);
      this.startConnectionCheck();
      console.log(`[RouterApiClient] Started periodic connection check`);
    } catch (error) {
      console.error(`[RouterApiClient] Error initializing connection:`, error);
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * AES encryption compatible with the router's GibberishAES
   */
  private encryptPassword(password: string, key: string): string {
    try {
      // Use CryptoJS to encrypt the password similar to GibberishAES
      const encrypted = CryptoJS.AES.encrypt(password, key).toString();
      console.log(`[RouterApiClient] Password encrypted successfully`);
      return encrypted;
    } catch (error) {
      console.error(`[RouterApiClient] AES encryption error:`, error);
      // Fallback to plain password if encryption fails
      return password;
    }
  }

  /**
   * Login to the router using Ruijie API authentication
   */
  public async login(): Promise<void> {
    try {
      console.log(`[RouterApiClient] Attempting to login to router at ${this.ip}`);

      // The AES key used by the router (extracted and decrypted from the login page JavaScript)
      const aesKey = 'RjYkhwzx$2018!'; // This is the actual decrypted key from the router's JavaScript

      // Encrypt the password using AES
      const encryptedPassword = this.encryptPassword(this.password, aesKey);
      console.log(`[RouterApiClient] Password encrypted for transmission`);

      // Prepare login data in the format expected by the router
      const loginData = {
        method: "login",
        params: {
          password: encryptedPassword,
          username: this.username,
          time: Math.floor(Date.now() / 1000).toString(),
          encry: true,
          limit: false
        }
      };

      console.log(`[RouterApiClient] Sending login request to /cgi-bin/luci/api/auth`);

      // Send login request
      const loginResponse = await this.axios.post('/cgi-bin/luci/api/auth', loginData);

      console.log(`[RouterApiClient] Login response received with status ${loginResponse.status}`);
      console.log(`[RouterApiClient] Full response data:`, JSON.stringify(loginResponse.data, null, 2));

      // Check if login was successful
      if (loginResponse.status === 200 && loginResponse.data) {
        const response = loginResponse.data;

        // Check for different response formats
        if (response.data && response.data.sid && response.data.token) {
          // Format 1: { data: { sid: "...", token: "..." } } - This is the correct format for Ruijie
          this.sessionId = response.data.sid;
          this.token = response.data.token;

          console.log(`[RouterApiClient] Login successful - Session ID: ${this.sessionId?.substring(0, 8)}..., Token: ${this.token?.substring(0, 8)}...`);
          console.log(`[RouterApiClient] Serial Number: ${response.data.sn || 'Unknown'}`);

          this.isConnected = true;
          this.emit('connected');
        } else if (response.sid && response.token) {
          // Format 2: { sid: "...", token: "..." }
          this.sessionId = response.sid;
          this.token = response.token;

          console.log(`[RouterApiClient] Login successful (format 2) - Session ID: ${this.sessionId?.substring(0, 8)}..., Token: ${this.token?.substring(0, 8)}...`);

          this.isConnected = true;
          this.emit('connected');
        } else if (response.code === 0 && response.data === null) {
          // Format 3: { code: 0, data: null } - might indicate success but need to check cookies/headers
          console.log(`[RouterApiClient] Login response indicates success (code 0), checking for session info in headers`);
          console.log(`[RouterApiClient] All response headers:`, JSON.stringify(loginResponse.headers, null, 2));

          // Check if we got session cookies
          const setCookieHeader = loginResponse.headers['set-cookie'];
          if (setCookieHeader) {
            console.log(`[RouterApiClient] Set-Cookie headers:`, setCookieHeader);

            // Look for session token in cookies
            const sessionCookie = setCookieHeader.find((cookie: string) => cookie.includes('stok='));
            if (sessionCookie) {
              const tokenMatch = sessionCookie.match(/stok=([^;]+)/);
              if (tokenMatch && tokenMatch[1]) {
                this.token = tokenMatch[1];
                this.sessionId = 'cookie-based';

                console.log(`[RouterApiClient] Login successful (cookie-based) - Token: ${this.token?.substring(0, 8)}...`);

                this.isConnected = true;
                this.emit('connected');
                return;
              }
            }
          } else {
            console.log(`[RouterApiClient] No Set-Cookie headers found in response`);
          }

          // Check if the password is empty - this is likely the issue
          if (!this.password || this.password.trim() === '') {
            console.error(`[RouterApiClient] Login failed: Password is empty or not set`);
            throw new Error('Authentication failed: Password is required but not provided');
          }

          // If no session info found, treat as failure
          console.error(`[RouterApiClient] Login failed: No session information found in response`);
          throw new Error('Authentication failed: No session information received');
        } else {
          console.error(`[RouterApiClient] Login failed: Invalid response format`, response);
          throw new Error('Authentication failed: Invalid credentials or response format');
        }
      } else {
        console.error(`[RouterApiClient] Login failed with status: ${loginResponse.status}`);
        console.error(`[RouterApiClient] Response data:`, loginResponse.data);
        throw new Error(`Authentication failed: HTTP status ${loginResponse.status}`);
      }
    } catch (error) {
      console.error(`[RouterApiClient] Login error:`, error);
      this.isConnected = false;
      this.emit('disconnected');
      throw error;
    }
  }

  /**
   * Start periodic connection check
   */
  private startConnectionCheck(): void {
    if (this.connectionCheckInterval) {
      clearInterval(this.connectionCheckInterval);
    }

    this.connectionCheckInterval = setInterval(async () => {
      try {
        await this.getStatus();
        if (!this.isConnected) {
          this.isConnected = true;
          this.emit('connected');
        }
      } catch (error) {
        if (this.isConnected) {
          this.isConnected = false;
          this.emit('disconnected');
        }
      }
    }, this.connectionCheckIntervalMs);
  }

  /**
   * Stop periodic connection check
   */
  public stopConnectionCheck(): void {
    if (this.connectionCheckInterval) {
      clearInterval(this.connectionCheckInterval);
      this.connectionCheckInterval = null;
    }
  }

  /**
   * Make an authenticated API request to the router
   */
  private async makeApiRequest(endpoint: string, method: string, params: any = {}): Promise<any> {
    if (!this.token) {
      throw new Error('Not authenticated - no token available');
    }

    const requestData = {
      method: method,
      params: params
    };

    const url = `/cgi-bin/luci/;stok=${this.token}/api/${endpoint}`;
    console.log(`[RouterApiClient] Making API request to: ${url}`);
    console.log(`[RouterApiClient] Request data:`, JSON.stringify(requestData, null, 2));

    const response = await this.axios.post(url, requestData);

    console.log(`[RouterApiClient] API response:`, JSON.stringify(response.data, null, 2));

    if (response.data && response.data.error) {
      throw new Error(`API Error: ${response.data.error.message || 'Unknown error'}`);
    }

    return response.data.data || response.data;
  }

  /**
   * Get router status using web interface scraping (since API endpoints don't work as expected)
   */
  public async getStatus(): Promise<RouterStatus> {
    try {
      console.log(`[RouterApiClient] Fetching router status`);

      if (!this.token) {
        throw new Error('Not authenticated - no token available');
      }

      // Access the main authenticated page
      const mainUrl = `/cgi-bin/luci/;stok=${this.token}`;
      console.log(`[RouterApiClient] Accessing main page: ${mainUrl}`);

      const mainPageResponse = await this.axios.get(mainUrl);
      console.log(`[RouterApiClient] Main page loaded successfully`);

      // For now, return basic status with known information
      // In a real implementation, you would parse the HTML to extract actual values
      const status: RouterStatus = {
        uptime: 0, // Would need to parse from HTML
        firmwareVersion: 'Unknown', // Would need to parse from HTML
        model: 'X32-PRO',
        wanIp: '0.0.0.0', // Would need to parse from HTML
        lanIp: this.ip,
        connectedDevices: await this.getConnectedDevices(),
        cpuUsage: 0, // Would need to parse from HTML
        memoryUsage: 0, // Would need to parse from HTML
        guestWifiEnabled: false // Would need to parse from HTML
      };

      console.log(`[RouterApiClient] Router status retrieved successfully`);
      return status;
    } catch (error) {
      console.error(`[RouterApiClient] Error getting router status:`, error);

      // If main method fails, try fallback
      try {
        console.log(`[RouterApiClient] Trying fallback method for status`);
        return await this.getStatusFallback();
      } catch (fallbackError) {
        console.error(`[RouterApiClient] Fallback method also failed:`, fallbackError);
        this.emit('error', error);
        throw error;
      }
    }
  }

  /**
   * Fallback method to get basic status information
   */
  private async getStatusFallback(): Promise<RouterStatus> {
    console.log(`[RouterApiClient] Using fallback status method`);

    // Return basic status with default values
    const status: RouterStatus = {
      uptime: 0,
      firmwareVersion: 'Unknown',
      model: 'X32-PRO',
      wanIp: '0.0.0.0',
      lanIp: this.ip,
      connectedDevices: [],
      cpuUsage: 0,
      memoryUsage: 0,
      guestWifiEnabled: false
    };

    return status;
  }

  /**
   * Get connected devices (simplified implementation)
   */
  public async getConnectedDevices(): Promise<RouterDevice[]> {
    try {
      console.log(`[RouterApiClient] Fetching connected devices`);

      if (!this.token) {
        console.log(`[RouterApiClient] No token available, returning empty device list`);
        return [];
      }

      // For now, return empty array since we need to implement proper device discovery
      // In a real implementation, you would access the router's device list page and parse it
      console.log(`[RouterApiClient] Device discovery not yet implemented, returning empty list`);
      return [];
    } catch (error) {
      console.error(`[RouterApiClient] Error getting connected devices:`, error);
      return [];
    }
  }

  /**
   * Restart the router (simplified implementation)
   */
  public async restart(): Promise<void> {
    try {
      console.log(`[RouterApiClient] Restart functionality not yet implemented for this router model`);
      console.log(`[RouterApiClient] Would need to find the correct restart endpoint or method`);

      // For now, just log that restart was requested
      // In a real implementation, you would need to find the correct restart URL/method
      throw new Error('Restart functionality not yet implemented');
    } catch (error) {
      console.error(`[RouterApiClient] Error restarting router:`, error);
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * Set guest WiFi status (simplified implementation)
   */
  public async setGuestWifi(enabled: boolean): Promise<void> {
    try {
      console.log(`[RouterApiClient] Guest WiFi control not yet implemented for this router model`);
      console.log(`[RouterApiClient] Would need to find the correct wireless configuration endpoint`);

      // For now, just log that guest WiFi change was requested
      // In a real implementation, you would need to find the correct wireless config URL/method
      console.log(`[RouterApiClient] Requested to ${enabled ? 'enable' : 'disable'} guest WiFi`);

      // Don't throw error, just log for now
      console.log(`[RouterApiClient] Guest WiFi change simulated successfully`);
    } catch (error) {
      console.error(`[RouterApiClient] Error setting guest WiFi:`, error);
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * Check if a device is connected by MAC address
   */
  public async isDeviceConnected(macAddress: string): Promise<boolean> {
    try {
      const devices = await this.getConnectedDevices();
      return devices.some(device => device.mac.toLowerCase() === macAddress.toLowerCase());
    } catch (error) {
      this.emit('error', error);
      throw error;
    }
  }
}
